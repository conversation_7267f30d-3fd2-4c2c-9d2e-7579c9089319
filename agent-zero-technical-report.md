# Agent Zero 技术调研报告

## 1. 项目概述

Agent Zero 是一个有机增长的个人AI助手框架，具有以下核心特征：

- **动态有机框架**：不是预定义的代理框架，而是设计为动态、有机增长和学习的系统
- **完全透明可定制**：所有组件都是可读、可理解、可定制和可交互的
- **计算机作为工具**：使用操作系统作为工具来完成任务，没有预编程的单一用途工具
- **多Agent协作**：支持层次化的多Agent协作，每个Agent都有上级和下级关系
- **持久化记忆**：具有持久化记忆，能够记住之前的解决方案、代码、事实和指令

## 2. 整体架构设计

### 2.1 系统架构图

```mermaid
graph TB
    subgraph "用户界面层"
        WebUI[Web UI]
        CLI[CLI Interface]
        API[REST API]
    end
    
    subgraph "核心框架层"
        AC[AgentContext]
        A0[Agent 0]
        A1[Agent 1]
        A2[Agent 2]
        AN[Agent N]
    end
    
    subgraph "工具系统层"
        Tools[Tool System]
        MCP[MCP Tools]
        Extensions[Extensions]
        Instruments[Instruments]
    end
    
    subgraph "模型层"
        ChatModel[Chat Model]
        UtilityModel[Utility Model]
        EmbeddingModel[Embedding Model]
        BrowserModel[Browser Model]
    end
    
    subgraph "存储层"
        Memory[Vector Memory]
        Knowledge[Knowledge Base]
        History[Message History]
        Logs[Execution Logs]
    end
    
    subgraph "提示词系统"
        SystemPrompts[System Prompts]
        ToolPrompts[Tool Prompts]
        BehaviorPrompts[Behavior Prompts]
    end
    
    WebUI --> API
    CLI --> AC
    API --> AC
    
    AC --> A0
    A0 --> A1
    A1 --> A2
    A2 --> AN
    
    A0 --> Tools
    A0 --> Extensions
    A0 --> Instruments
    
    A0 --> ChatModel
    A0 --> UtilityModel
    A0 --> EmbeddingModel
    A0 --> BrowserModel
    
    A0 --> Memory
    A0 --> Knowledge
    A0 --> History
    A0 --> Logs
    
    A0 --> SystemPrompts
    Tools --> ToolPrompts
    Extensions --> BehaviorPrompts
```

### 2.2 目录结构分析

```
agent-zero/
├── agent.py                 # 核心Agent实现
├── initialize.py            # 框架初始化
├── models.py               # 模型配置和管理
├── python/                 # 核心Python代码库
│   ├── api/               # API端点实现
│   ├── extensions/        # 扩展系统
│   ├── helpers/           # 工具函数
│   └── tools/             # 工具实现
├── prompts/               # 提示词系统
│   ├── default/          # 默认提示词
│   ├── agent0/           # Agent0专用提示词
│   └── [custom]/         # 自定义提示词
├── memory/                # 持久化记忆存储
├── knowledge/             # 知识库
├── webui/                 # Web用户界面
├── logs/                  # 执行日志
└── tmp/                   # 临时数据
```

## 3. Agent设计及执行流程

### 3.1 Agent核心架构

```mermaid
classDiagram
    class AgentContext {
        +String id
        +AgentConfig config
        +Agent agent0
        +Log log
        +DeferredTask task
        +communicate(msg)
        +reset()
        +nudge()
    }
    
    class Agent {
        +int number
        +String agent_name
        +AgentConfig config
        +History history
        +dict data
        +monologue()
        +process_tools(msg)
        +call_chat_model(prompt)
        +get_tool(name, method, args)
    }
    
    class AgentConfig {
        +ModelConfig chat_model
        +ModelConfig utility_model
        +ModelConfig embeddings_model
        +String prompts_subdir
        +String memory_subdir
        +list knowledge_subdirs
    }
    
    AgentContext --> Agent
    Agent --> AgentConfig
    Agent --> History
```

### 3.2 Agent执行流程

```mermaid
sequenceDiagram
    participant User
    participant AgentContext
    participant Agent
    participant Tool
    participant Model
    
    User->>AgentContext: communicate(message)
    AgentContext->>Agent: monologue()
    
    loop Message Loop
        Agent->>Agent: prepare_prompt()
        Agent->>Model: call_chat_model(prompt)
        Model-->>Agent: response
        Agent->>Agent: process_tools(response)
        
        alt Tool Found
            Agent->>Tool: execute(**args)
            Tool-->>Agent: tool_result
            Agent->>Agent: hist_add_tool_result()
        else No Tool
            Agent->>Agent: continue_loop
        end
        
        alt Break Loop
            Agent-->>AgentContext: final_response
        else Continue
            Agent->>Agent: next_iteration
        end
    end
    
    AgentContext-->>User: response
```

### 3.3 Agent状态管理

Agent的核心状态包括：
- **number**: Agent编号，决定层级关系
- **history**: 消息历史记录
- **data**: 自由数据对象，用于工具间数据共享
- **intervention**: 干预消息，用于实时控制
- **config**: 配置信息，包含模型和提示词设置

## 4. 多Agent协作机制

### 4.1 层次化协作架构

```mermaid
graph TD
    User[用户/Human] --> A0[Agent 0]
    A0 --> A1[Agent 1]
    A0 --> A2[Agent 2]
    A1 --> A11[Agent 1.1]
    A1 --> A12[Agent 1.2]
    A2 --> A21[Agent 2.1]
    
    A11 -.->|报告结果| A1
    A12 -.->|报告结果| A1
    A1 -.->|报告结果| A0
    A21 -.->|报告结果| A2
    A2 -.->|报告结果| A0
    A0 -.->|报告结果| User
```

### 4.2 Agent协作流程

```mermaid
sequenceDiagram
    participant Superior as 上级Agent
    participant Subordinate as 下级Agent
    participant Tool as call_subordinate工具
    
    Superior->>Tool: 调用call_subordinate
    Tool->>Subordinate: 创建/获取下级Agent
    Tool->>Subordinate: 设置superior关系
    Superior->>Subordinate: 发送任务消息
    
    loop 下级Agent执行
        Subordinate->>Subordinate: 处理任务
        Subordinate->>Subordinate: 使用工具
        alt 需要进一步委派
            Subordinate->>Subordinate: 创建更下级Agent
        end
    end
    
    Subordinate-->>Superior: 返回执行结果
    Superior->>Superior: 处理下级结果
```

### 4.3 协作数据结构

Agent间通过以下数据结构维护关系：
- `DATA_NAME_SUPERIOR`: 指向上级Agent的引用
- `DATA_NAME_SUBORDINATE`: 指向下级Agent的引用
- `intervention`: 用于上级向下级发送干预消息
- `broadcast_level`: 控制消息广播层级

## 5. 提示词系统设计

### 5.1 提示词架构

```mermaid
graph LR
    subgraph "提示词组织结构"
        Main[agent.system.main.md]
        Tools[agent.system.tools.md]
        Behavior[agent.system.behaviour.md]
        MCP[agent.system.mcp_tools.md]
    end
    
    subgraph "主要提示词组件"
        Role[agent.system.main.role.md]
        Comm[agent.system.main.communication.md]
        Solving[agent.system.main.solving.md]
        Tips[agent.system.main.tips.md]
        Env[agent.system.main.environment.md]
    end
    
    subgraph "工具提示词"
        ToolResp[agent.system.tool.response.md]
        ToolSub[agent.system.tool.call_sub.md]
        ToolMem[agent.system.tool.memory.md]
        ToolCode[agent.system.tool.code_exe.md]
    end
    
    Main --> Role
    Main --> Comm
    Main --> Solving
    Main --> Tips
    Main --> Env
    
    Tools --> ToolResp
    Tools --> ToolSub
    Tools --> ToolMem
    Tools --> ToolCode
```

### 5.2 提示词生成流程

```mermaid
sequenceDiagram
    participant Agent
    participant Extension
    participant FileSystem
    participant Template
    
    Agent->>Extension: call_extensions("message_loop_prompts_before")
    Extension->>Agent: 预处理提示词
    
    Agent->>Agent: get_system_prompt()
    Agent->>FileSystem: read_prompt("agent.system.main.md")
    FileSystem->>Template: process_includes()
    Template->>Template: replace_placeholders()
    Template-->>Agent: 完整系统提示词
    
    Agent->>Extension: call_extensions("message_loop_prompts_after")
    Extension->>Agent: 后处理提示词（添加记忆等）
    
    Agent->>Agent: 构建最终ChatPromptTemplate
```

### 5.3 提示词模板系统

提示词系统支持以下特性：
- **模板包含**: 使用`{{ include 'path' }}`语法
- **变量替换**: 使用`{{variable}}`语法
- **动态生成**: 通过扩展系统动态添加内容
- **层级覆盖**: 自定义提示词可覆盖默认提示词
- **工具集成**: 自动生成工具使用说明

## 6. 前后端通信协议设计

### 6.1 通信架构

```mermaid
graph TB
    subgraph "前端 (WebUI)"
        UI[用户界面]
        JS[JavaScript模块]
        API_Client[API客户端]
        SSE[Server-Sent Events]
    end
    
    subgraph "后端 (Flask)"
        Flask_App[Flask应用]
        API_Handlers[API处理器]
        Agent_Context[AgentContext]
        MCP_Proxy[MCP代理]
    end
    
    subgraph "核心系统"
        Agent_Core[Agent核心]
        Tools[工具系统]
        Memory[记忆系统]
    end
    
    UI --> JS
    JS --> API_Client
    API_Client --> Flask_App
    Flask_App --> API_Handlers
    API_Handlers --> Agent_Context
    Agent_Context --> Agent_Core
    Agent_Core --> Tools
    Agent_Core --> Memory
    
    Flask_App --> SSE
    SSE --> UI
    
    Flask_App --> MCP_Proxy

### 6.2 API接口设计

#### 核心API端点

| 端点 | 方法 | 功能 | 输入 | 输出 |
|------|------|------|------|------|
| `/message` | POST | 发送消息给Agent | `{text, context, message_id}` | `{message, context}` |
| `/message_async` | POST | 异步消息处理 | `{text, context, attachments}` | `{context}` |
| `/poll` | POST | 轮询日志更新 | `{context, log_from, timezone}` | `{logs, contexts, tasks}` |
| `/pause` | POST | 暂停/恢复Agent | `{paused, context}` | `{status}` |
| `/transcribe` | POST | 语音转文字 | `{audio}` | `{text}` |
| `/settings` | GET/POST | 获取/设置配置 | `{settings}` | `{settings}` |

#### API处理器架构

```mermaid
classDiagram
    class ApiHandler {
        +Flask app
        +threading.Lock thread_lock
        +process(input, request)
        +handle_request(request)
        +get_context(ctxid)
        +requires_auth()
        +requires_csrf()
    }

    class Message {
        +process(input, request)
        +communicate(input, request)
        +respond(task, context)
    }

    class Poll {
        +process(input, request)
        +get_logs(context, from_no)
        +get_contexts()
        +get_tasks()
    }

    class Settings {
        +process(input, request)
        +get_settings()
        +update_settings(data)
    }

    ApiHandler <|-- Message
    ApiHandler <|-- Poll
    ApiHandler <|-- Settings
```

### 6.3 实时通信机制

#### 轮询机制

```mermaid
sequenceDiagram
    participant WebUI
    participant Poll_API
    participant AgentContext
    participant Log

    loop 每秒轮询
        WebUI->>Poll_API: POST /poll {context, log_from}
        Poll_API->>AgentContext: get_context(ctxid)
        AgentContext->>Log: output(start=from_no)
        Log-->>Poll_API: logs[]
        Poll_API-->>WebUI: {logs, contexts, tasks}
        WebUI->>WebUI: 更新UI显示
    end
```

#### 消息流处理

```mermaid
graph LR
    subgraph "消息类型"
        User[user]
        Agent[agent]
        Tool[tool]
        Response[response]
        Error[error]
        Warning[warning]
        Info[info]
    end

    subgraph "UI渲染"
        UserMsg[用户消息]
        AgentMsg[Agent思考]
        ToolMsg[工具执行]
        RespMsg[最终回复]
        ErrMsg[错误提示]
    end

    User --> UserMsg
    Agent --> AgentMsg
    Tool --> ToolMsg
    Response --> RespMsg
    Error --> ErrMsg
    Warning --> ErrMsg
    Info --> ErrMsg
```

### 6.4 文件上传和附件处理

支持多种文件类型的上传和处理：
- **图片文件**: 自动转换为base64并支持视觉模型分析
- **文档文件**: 支持PDF、TXT、MD等格式的知识库导入
- **代码文件**: 直接作为上下文提供给Agent

## 7. 上下文和Memory设计

### 7.1 Memory系统架构

```mermaid
graph TB
    subgraph "Memory系统"
        Memory[Memory管理器]
        FAISS[FAISS向量数据库]
        Embeddings[嵌入模型]
        Documents[文档存储]
    end

    subgraph "Memory区域"
        Main[主要记忆区域]
        Fragments[对话片段]
        Solutions[解决方案]
        Instruments[工具记录]
    end

    subgraph "知识库"
        Default[默认知识库]
        Custom[自定义知识库]
        Import[知识导入器]
    end

    Memory --> FAISS
    Memory --> Embeddings
    Memory --> Documents

    FAISS --> Main
    FAISS --> Fragments
    FAISS --> Solutions
    FAISS --> Instruments

    Import --> Default
    Import --> Custom
    Import --> FAISS
```

### 7.2 Memory操作流程

```mermaid
sequenceDiagram
    participant Agent
    participant Memory
    participant FAISS
    participant Embeddings

    Agent->>Memory: search_similarity_threshold(query)
    Memory->>Embeddings: embed_query(query)
    Embeddings-->>Memory: query_vector
    Memory->>FAISS: asearch(query_vector, threshold)
    FAISS-->>Memory: similar_documents[]
    Memory-->>Agent: formatted_results

    Agent->>Memory: insert_text(text, metadata)
    Memory->>Embeddings: embed_documents([text])
    Embeddings-->>Memory: document_vectors
    Memory->>FAISS: aadd_documents(docs, vectors)
    FAISS-->>Memory: document_ids[]
    Memory-->>Agent: success_response
```

### 7.3 历史记录管理

```mermaid
classDiagram
    class History {
        +list~Bulk~ bulks
        +list~Topic~ topics
        +Topic current
        +Agent agent
        +get_tokens()
        +is_over_limit()
        +add_message(ai, content)
        +new_topic()
    }

    class Topic {
        +History history
        +string summary
        +list~Message~ messages
        +get_tokens()
        +add_message(ai, content)
        +summarize()
    }

    class Message {
        +bool ai
        +MessageContent content
        +int tokens
        +get_tokens()
        +output()
    }

    class Bulk {
        +string summary
        +get_tokens()
        +output()
    }

    History --> Topic
    History --> Bulk
    Topic --> Message
```

### 7.4 上下文窗口管理

Agent Zero采用智能的上下文窗口管理策略：

1. **分层摘要**: 将历史消息分为Bulk、Topic和Current三个层级
2. **动态压缩**: 当上下文超出限制时自动进行摘要压缩
3. **记忆检索**: 通过向量搜索检索相关历史信息
4. **Token计算**: 精确计算各部分的Token使用量

## 8. 工具系统设计

### 8.1 工具架构

```mermaid
classDiagram
    class Tool {
        +Agent agent
        +string name
        +string method
        +dict args
        +string message
        +execute(**kwargs)
        +before_execution(**kwargs)
        +after_execution(response)
    }

    class Response {
        +string message
        +bool break_loop
    }

    class CodeExecution {
        +execute(runtime, code, session)
        +handle_docker_execution()
        +handle_ssh_execution()
    }

    class Memory {
        +execute(query, threshold, limit)
        +search_similarity()
        +insert_documents()
    }

    class Knowledge {
        +execute(question)
        +searxng_search()
        +mem_search()
    }

    Tool <|-- CodeExecution
    Tool <|-- Memory
    Tool <|-- Knowledge
    Tool --> Response
```

### 8.2 MCP工具集成

```mermaid
sequenceDiagram
    participant Agent
    participant MCPHandler
    participant MCPServer
    participant Tool

    Agent->>MCPHandler: get_tool(tool_name)
    MCPHandler->>MCPServer: list_tools()
    MCPServer-->>MCPHandler: available_tools[]
    MCPHandler->>MCPServer: call_tool(name, args)
    MCPServer-->>MCPHandler: tool_result
    MCPHandler-->>Agent: formatted_response
```

## 9. 扩展系统设计

### 9.1 扩展架构

扩展系统允许在Agent执行的关键节点插入自定义逻辑：

- **monologue_start**: Agent开始独白前
- **message_loop_start**: 消息循环开始前
- **message_loop_prompts_before**: 构建提示词前
- **message_loop_prompts_after**: 构建提示词后
- **response_stream**: 响应流处理中

### 9.2 关键扩展实现

#### 记忆召回扩展

```mermaid
graph LR
    subgraph "记忆召回流程"
        Query[提取查询]
        Search[向量搜索]
        Filter[结果过滤]
        Format[格式化]
        Inject[注入提示词]
    end

    Query --> Search
    Search --> Filter
    Filter --> Format
    Format --> Inject
```

#### 行为规则扩展

动态加载和应用行为规则：
- 从`memory/behaviour.md`加载自定义规则
- 将规则注入到系统提示词中
- 支持运行时规则更新

## 10. 技术特点总结

### 10.1 核心优势

1. **高度可定制性**: 所有组件都可以通过配置文件和提示词进行定制
2. **有机增长**: 系统能够通过记忆和学习不断改进
3. **层次化协作**: 支持复杂任务的分解和多Agent协作
4. **工具生态**: 丰富的内置工具和MCP工具集成
5. **实时交互**: 支持实时干预和控制

### 10.2 技术创新点

1. **提示词驱动**: 整个框架行为完全由提示词定义
2. **计算机作为工具**: 不限制Agent的能力，让其自由使用系统资源
3. **记忆持久化**: 向量数据库支持的长期记忆系统
4. **流式处理**: 实时的响应流和进度反馈
5. **多模态支持**: 集成视觉、语音等多种模态

### 10.3 应用场景

- **软件开发**: 代码生成、调试、项目管理
- **数据分析**: 数据处理、可视化、报告生成
- **内容创作**: 文档编写、博客创作、技术文档
- **系统管理**: 服务器管理、监控、自动化运维
- **研究辅助**: 文献调研、数据收集、分析总结

## 11. 技术栈总结

### 11.1 后端技术栈

- **核心框架**: Python + Flask
- **AI模型**: LangChain + 多种LLM提供商
- **向量数据库**: FAISS
- **文档处理**: LangChain Document Loaders
- **异步处理**: asyncio + threading
- **容器化**: Docker

### 11.2 前端技术栈

- **基础技术**: HTML5 + CSS3 + Vanilla JavaScript
- **实时通信**: 轮询 + Server-Sent Events
- **语音处理**: Web Speech API + Transformers.js
- **文件处理**: FormData + File API
- **UI组件**: 自定义组件系统

### 11.3 部署和运维

- **容器化部署**: Docker + Docker Compose
- **配置管理**: 环境变量 + JSON配置
- **日志系统**: 结构化日志 + HTML日志
- **监控**: 内置进度监控和状态管理

这个技术调研报告全面分析了Agent Zero项目的架构设计、技术实现和创新特点，为理解和使用该框架提供了详细的技术参考。
