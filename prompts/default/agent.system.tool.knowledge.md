### knowledge_tool:
provide your question as a search engine - ready query
the first responder will be a search engine not an ai or human
formulate your query as concisely and precisely as possible
provide question arg get online and memory response
powerful tool answers specific questions directly
ask for result first not guidance
memory gives guidance online gives current info
verify memory with online
**Example usage**:
~~~json
{
    "thoughts": [
        "...",
    ],
    "tool_name": "knowledge_tool",
    "tool_args": {
        "question": "How to...",
    }
}
~~~
